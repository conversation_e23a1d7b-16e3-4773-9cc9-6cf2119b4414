#发布流程梳理
## Global variables
variables:
  # 服务名: 用于容器，日志的创建
  SERVER_NAME: "webview-channel"
  # 容器: 运行的服务端口
  SERVICE_PORT: 80
  # 构建命令
  VUE_INSTALL: "npm install"
  # 构建命令 (CI环境跳过类型检查以加速构建)
  VUE_BUILD: "npm run build"
  VUE_BUILD_CI: "npm run build"
  # 镜像标签
  DOCKER_TAG: "${K8S_NAMESPACE}_${CI_COMMIT_SHORT_SHA}_${CI_PIPELINE_ID}"
  # 镜像文件
  DOCKERFILE_PATH: './.gitlab/Dockerfile'
  # 模版: Kubernetes创建相关资源的模板。主要是Deploymentod, Service, AliLogs
  K8S_TEMPLATES: ".gitlab/k8s_template.yml"
  # 模版目录: 保存K8S的命令
  K8S_TEMPLATES_DIR: "./deploy/${K8S_NAMESPACE}"
  # 镜像地址
  REGISTRY_HOST: crpi-n101a83isbuxrjr0-vpc.cn-hangzhou.personal.cr.aliyuncs.com
  # 镜像仓库
  DOCKER_REPO: car_test
  # 预留变量
  PROJECT: car
  # 人员: 来自CICD传来的变量
  RUN_JOB_USER: $RUN_JOB_USER
  # 描述: 来自CICD传来的变量
  RUN_JOB_DESC: $RUN_JOB_DESC
  # NGINX模版
  NGINX_TEMPLATES: ".gitlab/nginx-template.conf"
  # 该项目钉钉通知TOKEN，用于webhook通知
  DING_API_SECRET: ${DING_API_SECRET}
  # 该项目钉钉通知API，用于webhook通知
  DING_API_TOKEN: ${DING_API_TOKEN}
  # 指定Node版本
  NODE_VERSION: /usr/local/node18/bin
  # Node代理
  HTTP_PROXY: ${HTTP_PROXY}

stages:
  - trigger


车生活-webview-topic-channel(K8S)-测试:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-k8s.yml
    strategy: depend
  variables:
    # 测试环境的RUNNER服务器
    RUNNER_TAGS: 'CAR_TEST_NODE'
    # 显示指定测试环境用于差异化构建
    DEPLOY_ENV: 'test'
    # 容器相关: 命名空间
    K8S_NAMESPACE: "car-test"
    # 容器相关: 副本集
    POD_SPEC: 1
    # 容器相关: 调度请求资源
    REQUEST_MEM: '256M'
    # 容器相关: 调度请求资源
    REQUEST_CPU: '0.25'
    # 容器相关: 运行限制资源
    LIMITED_MEM: '512G'
    # 容器相关: 运行限制资源
    LIMITED_CPU: '0.5'
    # 容器相关: runner部署资源需要的地址
    K8S_ADDRESS: ${CAR_TEST_K8S}
  only:
    refs:
      - test
    variables:
      - $PROJECT_JOB == "CAR_CHANNEL_TEST"


车生活-webview-topic-channel(OSS)-测试:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-oss.yml
    strategy: depend
  variables:
    # 测试环境的RUNNER服务器
    RUNNER_TAGS: 'CAR_TEST_NODE'
    # 显示指定测试环境用于差异化构建
    DEPLOY_ENV: 'test'
    # OSS存储桶
    OSS_BUCKET: 'car-webview-topic-channel-test'
  only:
    refs:
      - test
    variables:
      - $PROJECT_JOB == "CAR_CHANNEL_OSS_TEST"

车生活-webview-topic-channel(K8S)-预发:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-k8s.yml
    strategy: depend
  variables:
    # 差异化变量参考测试段
    RUNNER_TAGS: 'CAR_PRE_NODE'
    DEPLOY_ENV: 'pre'
    K8S_NAMESPACE: "car-pre"
    POD_SPEC: 1
    REQUEST_MEM: '256M'
    REQUEST_CPU: '0.25'
    LIMITED_MEM: '512G'
    LIMITED_CPU: '0.5'
    K8S_ADDRESS: ${CAR_PRE_K8S}
  only:
    refs:
      - pre-release
    variables:
      - $PROJECT_JOB == "CAR_CHANNEL_PRE"


车生活-webview-topic-channel(OSS)-预发:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-oss.yml
    strategy: depend
  variables:
    # 预发环境的RUNNER服务器
    RUNNER_TAGS: 'CAR_PRE_NODE'
    # 显示指定测试环境用于差异化构建
    DEPLOY_ENV: 'pre'
    # OSS存储桶
    OSS_BUCKET: 'car-webview-topic-channel-pre'
  only:
    refs:
      - pre-release
    variables:
      - $PROJECT_JOB == "CAR_CHANNEL_OSS_PRE"


车生活-webview-topic-channel(OSS)-生产:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-oss.yml
    strategy: depend
  variables:
    # 容器运行节点
    RUNNER_TAGS: 'CAR_PROD_NODE'
    DEPLOY_ENV: 'prod'
    # OSS存储通
    OSS_BUCKET: 'car-webview-topic-channel'
  only:
    refs:
      - master
    variables:
      - $PROJECT_JOB == "CAR_CHANNEL_OSS_PROD"


车生活-webview-topic-channel(K8S)-生产:
  stage: trigger
  trigger:
    include: .gitlab/ci-deploy-k8s.yml
    strategy: depend
  variables:
    RUNNER_TAGS: 'CAR_PROD_NODE'
    DEPLOY_ENV: 'prod'
    K8S_NAMESPACE: "car-pro"
    POD_SPEC: 2
    REQUEST_MEM: '256M'
    REQUEST_CPU: '0.25'
    LIMITED_MEM: '512G'
    LIMITED_CPU: '0.5'
    K8S_ADDRESS: ${CAR_PROD_K8S}
  only:
    refs:
      - master
    variables:
      - $PROJECT_JOB == "CAR_CHANNEL_K8S_PROD"
