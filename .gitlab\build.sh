PATH=${NODE_VERSION}:$PATH
node -v | xargs echo "node version: "

if  grep -q "3DA1" /proc/net/tcp ; then
    # 使用代理
    echo "加速器模式: 本地代理"
    PROXY='--proxy http://127.0.0.1:15777'
else
    PROXY=''
    echo "加速器模式: 使用淘宝镜像加速"
    npm config set registry https://registry.npmmirror.com/
fi


# 构建信息
set -x
set -e

echo "------------------------------安装模块------------------------------"
${VUE_INSTALL} ${PROXY}

echo "------------------------------开始构建------------------------------"
echo "构建环境: ${DEPLOY_ENV}"
echo "构建开始时间: $(date)"
echo "Node版本: $(node -v)"
echo "NPM版本: $(npm -v)"

# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 创建进度监控函数
monitor_build() {
    local build_cmd="$1"
    local log_file="/tmp/build_output.log"
    local pid_file="/tmp/build.pid"

    echo "执行构建命令: $build_cmd"
    echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

    # 后台执行构建并记录PID
    $build_cmd > $log_file 2>&1 &
    local build_pid=$!
    echo $build_pid > $pid_file

    echo "构建进程PID: $build_pid"

    # 监控构建进度
    local counter=0
    local last_size=0

    while kill -0 $build_pid 2>/dev/null; do
        sleep 30  # 每30秒检查一次
        counter=$((counter + 1))

        # 检查日志文件大小变化
        if [ -f "$log_file" ]; then
            local current_size=$(wc -c < "$log_file" 2>/dev/null || echo 0)
            echo "[监控 ${counter}] 运行时间: $((counter * 30))秒, 日志大小: ${current_size}字节 (+$((current_size - last_size)))"

            # 显示最新的构建输出
            echo "[最新输出]:"
            tail -n 3 "$log_file" 2>/dev/null || echo "暂无输出"
            echo "----------------------------------------"

            last_size=$current_size
        fi

        # 检查是否超过合理时间（15分钟）
        if [ $counter -gt 30 ]; then
            echo "警告: 构建已运行超过15分钟，可能存在问题"
            echo "当前进程状态:"
            ps aux | grep $build_pid | grep -v grep || echo "进程不存在"
        fi
    done

    # 等待进程结束并获取退出码
    wait $build_pid
    local exit_code=$?

    echo "构建进程结束，退出码: $exit_code"
    echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

    # 显示完整的构建输出
    if [ -f "$log_file" ]; then
        echo "------------------------------完整构建日志------------------------------"
        cat "$log_file"
        echo "------------------------------构建日志结束------------------------------"
    fi

    # 清理临时文件
    rm -f "$log_file" "$pid_file"

    return $exit_code
}

# 执行构建并监控进度
if monitor_build "${VUE_BUILD}:${DEPLOY_ENV}"; then
    echo "✅ 构建成功完成"
else
    echo "❌ 构建失败，退出码: $?"
    exit 1
fi

echo "------------------------------构建完成------------------------------"
echo "构建完成时间: $(date)"

# 检查构建产物
if [ -d "dist" ]; then
    echo "✅ dist目录存在"
    echo "构建产物大小:"
    du -sh dist/
    echo "主要文件:"
    find dist/ -name "*.js" -o -name "*.css" -o -name "*.html" | head -10
else
    echo "❌ dist目录不存在，构建可能失败"
    exit 1
fi

