PATH=${NODE_VERSION}:$PATH
node -v | xargs echo "node version: "

if  grep -q "3DA1" /proc/net/tcp ; then
    # 使用代理
    echo "加速器模式: 本地代理"
    PROXY='--proxy http://127.0.0.1:15777'
else
    PROXY=''
    echo "加速器模式: 使用淘宝镜像加速"
    npm config set registry https://registry.npmmirror.com/
fi


# 构建信息
set -x
set -e

echo "------------------------------安装模块------------------------------"
${VUE_INSTALL} ${PROXY}

echo "------------------------------开始构建------------------------------"
echo "构建环境: ${DEPLOY_ENV}"
echo "构建时间: $(date)"

# 设置Node.js内存限制和构建超时
export NODE_OPTIONS="--max-old-space-size=4096"

# 执行构建并显示进度 (CI环境使用快速构建)
timeout 600 ${VUE_BUILD}:${DEPLOY_ENV}-ci || {
    echo "CI构建失败，尝试完整构建..."
    timeout 600 ${VUE_BUILD}:${DEPLOY_ENV} || {
        echo "构建超时或失败，退出码: $?"
        exit 1
    }
}

echo "------------------------------构建完成------------------------------"
echo "构建完成时间: $(date)"
ls -la dist/ || echo "dist目录不存在"

