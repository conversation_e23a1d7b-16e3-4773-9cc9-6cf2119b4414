/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArrowBottom: typeof import('./../components/Icons/ArrowBottom.vue')['default']
    ArrowLeft: typeof import('./../components/Icons/ArrowLeft.vue')['default']
    BackTop: typeof import('./../components/BackTop/index.vue')['default']
    CarStatus: typeof import('./../components/CarStatus/index.vue')['default']
    CarStatus2: typeof import('./../components/CarStatus/CarStatus2.vue')['default']
    CircleLoading: typeof import('./../components/Icons/CircleLoading.vue')['default']
    CompanyPageFooter: typeof import('./../components/CompanyPageFooter/index.vue')['default']
    ConsentAuthorization: typeof import('./../components/Dialog/ConsentAuthorization.vue')['default']
    ConsentAuthorization2: typeof import('./../components/Dialog/ConsentAuthorization2.vue')['default']
    ConsentAuthorizationIntercept: typeof import('./../components/Dialog/ConsentAuthorizationIntercept.vue')['default']
    DialogDownload: typeof import('./../components/Dialog/DialogDownload.vue')['default']
    DialogDownloadRocket: typeof import('./../components/Dialog/DialogDownloadRocket.vue')['default']
    DialogError: typeof import('./../components/Dialog/DialogError.vue')['default']
    DialogIdentityCard: typeof import('./../components/Dialog/DialogIdentityCard.vue')['default']
    DialogIdentityCardNotice: typeof import('./../components/Dialog/DialogIdentityCardNotice.vue')['default']
    DialogLoading: typeof import('./../components/Dialog/DialogLoading.vue')['default']
    DialogLogin: typeof import('./../components/Dialog/DialogLogin.vue')['default']
    DialogLogin2: typeof import('./../components/Dialog/DialogLogin2.vue')['default']
    DialogPlateNumberCard: typeof import('./../components/Dialog/DialogPlateNumberCard.vue')['default']
    DialogSms: typeof import('./../components/Dialog/DialogSms.vue')['default']
    DialogView: typeof import('./../components/Dialog/DialogView.vue')['default']
    GaugeEcharts: typeof import('./../components/GaugeEcharts/index.vue')['default']
    IconCheck: typeof import('./../components/Icons/IconCheck.vue')['default']
    IconCheckWhite: typeof import('./../components/IconCheckWhite/index.vue')['default']
    IdentityCard: typeof import('./../components/IdentityCard/index.vue')['default']
    Loading: typeof import('./../components/Loading/index.vue')['default']
    NavBar: typeof import('./../components/NavBar/index.vue')['default']
    NoticeBar: typeof import('./../components/NoticeBar/index.vue')['default']
    Offline: typeof import('./../components/Icons/Offline.vue')['default']
    PageFooter: typeof import('./../components/PageFooter/index.vue')['default']
    PageFooter2: typeof import('./../components/PageFooter/PageFooter2.vue')['default']
    PlateNumber: typeof import('./../components/PlateNumber/index.vue')['default']
    PlateNumberKeyboard: typeof import('./../components/PlateNumberKeyboard/index.vue')['default']
    PlateNumberSwipe: typeof import('./../components/PlateNumberSwipe/index.vue')['default']
    Protocols: typeof import('./../components/Dialog/Protocols.vue')['default']
    QuestionTips: typeof import('./../components/QuestionTips/index.vue')['default']
    RegionPicker: typeof import('./../components/RegionPicker/index.vue')['default']
    RollingNumber: typeof import('./../components/RollingNumber/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatusBar: typeof import('./../components/StatusBar/index.vue')['default']
    VanCascader: typeof import('vant/es')['Cascader']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanIcon: typeof import('vant/es')['Icon']
    VanLoading: typeof import('vant/es')['Loading']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPopup: typeof import('vant/es')['Popup']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    WarmReminderNoticeBar: typeof import('./../components/WarmReminderNoticeBar/index.vue')['default']
    WechatProvider: typeof import('./../components/WechatProvider/index.vue')['default']
  }
}
