#定义执行步骤
stages:
  - build
  - deploy
  - cleanup

构建:
  stage: build
  tags:
    - ${RUNNER_TAGS}
  environment: ${DEPLOY_ENV}
  timeout: 20m  # 增加超时时间到20分钟
  retry:
    max: 2
    when:
      - script_failure
      - job_execution_timeout
  script:
    - |-
      echo "开始执行构建脚本..."
      sh ./.gitlab/build.sh
      echo "构建脚本执行完成，检查构建结果..."
      if [ ! -d "dist" ]; then
        echo "错误: dist目录不存在，构建失败"
        exit 1
      fi
      echo "构建产物检查通过"
    - |-
      date && echo "------------------------------构建Docker------------------------------"
      envsubst '${SERVER_NAME}' < ${NGINX_TEMPLATES} > ./.gitlab/${SERVER_NAME}.conf
      docker login -u ${REGISTRY_USERNAME} -p ${REGISTRY_PASSWORD} ${REGISTRY_HOST}
      echo "已登录docker"
      envsubst < ${DOCKERFILE_PATH} | docker build -f - -t  ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG} .
      docker push ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG}
      docker rmi ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG}


发版:
  stage: deploy
  tags:
    - ${RUNNER_TAGS}
  environment: ${DEPLOY_ENV}
  dependencies:
     - 构建
  script:
    - |-
      KUBECONFIG_DIR=${HOME}/k8s_config/${K8S_NAMESPACE}/
      export KUBECONFIG="${KUBECONFIG_DIR}/config"
      mkdir -p $KUBECONFIG_DIR ${K8S_TEMPLATES_DIR}
      echo ${K8S_ADDRESS} |base64 -d > $KUBECONFIG
    - |-
      envsubst < ${K8S_TEMPLATES} | tee ${K8S_TEMPLATES_DIR}/${SERVER_NAME}.yml | kubectl apply --record=true -f -
      echo "部署kubernetes"
      kubectl -n ${K8S_NAMESPACE} rollout status deployment ${SERVER_NAME}

清理:
  stage: cleanup
  tags:
    - ${RUNNER_TAGS}
  dependencies:
     - 发版
  script:
    - rm -rf ${CI_PROJECT_DIR}
