#定义执行步骤
stages:
  - build
  - deploy
  - cleanup

构建:
  stage: build
  tags:
    - ${RUNNER_TAGS}
  environment: ${DEPLOY_ENV}
  timeout: 20m  # 增加超时时间到20分钟
  retry:
    max: 2
    when:
      - script_failure
      - job_execution_timeout
  script:
    - |-
      echo "=========================================="
      echo "🚀 开始前端构建任务"
      echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
      echo "分支: ${CI_COMMIT_REF_NAME}"
      echo "提交: ${CI_COMMIT_SHORT_SHA}"
      echo "环境: ${DEPLOY_ENV}"
      echo "Runner: ${CI_RUNNER_DESCRIPTION}"
      echo "=========================================="

      # 显示系统资源信息
      echo "系统资源信息:"
      echo "CPU核数: $(nproc)"
      echo "内存信息: $(free -h | grep Mem)"
      echo "磁盘空间: $(df -h . | tail -1)"
      echo "----------------------------------------"

      # 执行构建脚本
      echo "📦 开始执行构建脚本..."
      sh ./.gitlab/build.sh

      echo "✅ 构建脚本执行完成，验证构建结果..."
      if [ ! -d "dist" ]; then
        echo "❌ 错误: dist目录不存在，构建失败"
        exit 1
      fi

      # 详细的构建产物检查
      echo "📊 构建产物详细信息:"
      echo "dist目录大小: $(du -sh dist/ | cut -f1)"
      echo "文件数量: $(find dist/ -type f | wc -l)"
      echo "主要文件列表:"
      find dist/ -name "*.js" -o -name "*.css" -o -name "*.html" | sort
      echo "✅ 构建产物检查通过"
    - |-
      date && echo "------------------------------构建Docker------------------------------"
      envsubst '${SERVER_NAME}' < ${NGINX_TEMPLATES} > ./.gitlab/${SERVER_NAME}.conf
      docker login -u ${REGISTRY_USERNAME} -p ${REGISTRY_PASSWORD} ${REGISTRY_HOST}
      echo "已登录docker"
      envsubst < ${DOCKERFILE_PATH} | docker build -f - -t  ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG} .
      docker push ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG}
      docker rmi ${REGISTRY_HOST}/${DOCKER_REPO}/${SERVER_NAME}:${DOCKER_TAG}


发版:
  stage: deploy
  tags:
    - ${RUNNER_TAGS}
  environment: ${DEPLOY_ENV}
  dependencies:
     - 构建
  script:
    - |-
      KUBECONFIG_DIR=${HOME}/k8s_config/${K8S_NAMESPACE}/
      export KUBECONFIG="${KUBECONFIG_DIR}/config"
      mkdir -p $KUBECONFIG_DIR ${K8S_TEMPLATES_DIR}
      echo ${K8S_ADDRESS} |base64 -d > $KUBECONFIG
    - |-
      envsubst < ${K8S_TEMPLATES} | tee ${K8S_TEMPLATES_DIR}/${SERVER_NAME}.yml | kubectl apply --record=true -f -
      echo "部署kubernetes"
      kubectl -n ${K8S_NAMESPACE} rollout status deployment ${SERVER_NAME}

清理:
  stage: cleanup
  tags:
    - ${RUNNER_TAGS}
  dependencies:
     - 发版
  script:
    - rm -rf ${CI_PROJECT_DIR}
