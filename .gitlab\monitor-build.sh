#!/bin/bash

# Vite构建进度监控脚本
# 用于实时监控构建过程，识别卡住的阶段

BUILD_CMD="$1"
LOG_FILE="/tmp/vite_build.log"
PROGRESS_FILE="/tmp/build_progress.txt"

echo "🔍 Vite构建监控器启动"
echo "监控命令: $BUILD_CMD"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 清理之前的日志
rm -f "$LOG_FILE" "$PROGRESS_FILE"

# 启动构建进程
echo "启动构建进程..."
$BUILD_CMD > "$LOG_FILE" 2>&1 &
BUILD_PID=$!

echo "构建进程PID: $BUILD_PID"

# 监控函数
monitor_progress() {
    local stage=""
    local last_line=""
    local stuck_counter=0
    local check_interval=15  # 15秒检查一次
    
    while kill -0 $BUILD_PID 2>/dev/null; do
        sleep $check_interval
        
        if [ -f "$LOG_FILE" ]; then
            # 获取最新的几行输出
            local recent_output=$(tail -n 5 "$LOG_FILE" 2>/dev/null)
            local current_line=$(tail -n 1 "$LOG_FILE" 2>/dev/null)
            
            # 检测构建阶段
            if echo "$recent_output" | grep -q "building for"; then
                stage="🏗️  正在构建"
            elif echo "$recent_output" | grep -q "transforming"; then
                stage="🔄 转换模块"
            elif echo "$recent_output" | grep -q "rendering chunks"; then
                stage="📦 渲染chunks"
            elif echo "$recent_output" | grep -q "computing gzip size"; then
                stage="📊 计算gzip大小"
            elif echo "$recent_output" | grep -q "dist/"; then
                stage="✅ 生成文件"
            fi
            
            # 检查是否卡住
            if [ "$current_line" = "$last_line" ]; then
                stuck_counter=$((stuck_counter + 1))
            else
                stuck_counter=0
            fi
            
            # 输出进度信息
            local runtime=$(($(date +%s) - $(stat -c %Y "$LOG_FILE" 2>/dev/null || date +%s)))
            echo "[$(date '+%H:%M:%S')] $stage | 运行时间: ${runtime}s | 卡住计数: $stuck_counter"
            
            # 显示最新输出
            if [ -n "$current_line" ] && [ "$current_line" != "$last_line" ]; then
                echo "  📝 最新: $current_line"
            fi
            
            # 如果卡住超过3分钟，显示详细信息
            if [ $stuck_counter -gt 12 ]; then  # 12 * 15s = 3分钟
                echo "⚠️  警告: 构建可能卡住，已等待 $((stuck_counter * check_interval)) 秒"
                echo "  当前阶段: $stage"
                echo "  进程状态: $(ps -p $BUILD_PID -o pid,ppid,state,time,cmd --no-headers 2>/dev/null || echo '进程不存在')"
                echo "  系统负载: $(uptime)"
                echo "  内存使用: $(free -m | grep Mem | awk '{printf "%.1f%%", $3/$2*100}')"
                
                # 显示最近的构建输出
                echo "  最近10行输出:"
                tail -n 10 "$LOG_FILE" 2>/dev/null | sed 's/^/    /'
            fi
            
            last_line="$current_line"
        else
            echo "[$(date '+%H:%M:%S')] 等待日志文件生成..."
        fi
    done
}

# 启动监控
monitor_progress &
MONITOR_PID=$!

# 等待构建完成
wait $BUILD_PID
BUILD_EXIT_CODE=$?

# 停止监控
kill $MONITOR_PID 2>/dev/null

echo ""
echo "🏁 构建进程结束"
echo "退出码: $BUILD_EXIT_CODE"
echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 显示构建摘要
if [ -f "$LOG_FILE" ]; then
    echo ""
    echo "📋 构建摘要:"
    
    # 统计关键信息
    local total_lines=$(wc -l < "$LOG_FILE" 2>/dev/null || echo 0)
    local warnings=$(grep -c "warn" "$LOG_FILE" 2>/dev/null || echo 0)
    local errors=$(grep -c "error\|Error\|ERROR" "$LOG_FILE" 2>/dev/null || echo 0)
    
    echo "  总日志行数: $total_lines"
    echo "  警告数量: $warnings"
    echo "  错误数量: $errors"
    
    # 显示最后几行重要输出
    echo ""
    echo "📄 最后10行输出:"
    tail -n 10 "$LOG_FILE" | sed 's/^/  /'
    
    # 如果有错误，显示错误信息
    if [ $errors -gt 0 ]; then
        echo ""
        echo "❌ 错误信息:"
        grep -i "error" "$LOG_FILE" | tail -n 5 | sed 's/^/  /'
    fi
fi

# 清理临时文件
rm -f "$LOG_FILE" "$PROGRESS_FILE"

exit $BUILD_EXIT_CODE
