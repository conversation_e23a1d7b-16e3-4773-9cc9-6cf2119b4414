import path from 'node:path'
import process from 'node:process'
import { loadEnv } from 'vite'
import type { ConfigEnv, UserConfig } from 'vite'
import { createVitePlugins } from './build/vite'
import { exclude, include } from './build/vite/optimize'

export default ({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd()
  const env = loadEnv(mode, root)

  return {
    base: env.VITE_APP_PUBLIC_PATH,
    plugins: createVitePlugins(mode),

    server: {
      host: true,
      port: 3001,
      proxy: {
        '/api': {
          target: 'https://tapi.haoxincd.cn', // 测试环境
          // target: 'http://*************:18085', // 龙太平
          // target: 'http://*************:18085', // 王偲翰
          ws: false,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
        },
      },
    },

    resolve: {
      alias: {
        '@': path.join(__dirname, './src'),
        '~': path.join(__dirname, './src/assets'),
        '~root': path.join(__dirname, '.'),
      },
    },

    build: {
      cssCodeSplit: false,
      chunkSizeWarningLimit: 2048,
      outDir: env.VITE_APP_OUT_DIR || 'dist',
      rollupOptions: {
        // 排除打包vue-pdf-embed，使用public/cdn/vue-pdf-embed-umd.js
        external: ['vue-pdf-embed'],
      },
    },

    optimizeDeps: { include, exclude },
  }
}
